# 新标签页功能使用指南

## 概述

`navigate_to_service` 方法现在支持在新标签页中打开服务，提供更灵活的测试用例编写方式。

## 功能特性

### 1. 基础新标签页功能

```python
# 在新标签页中打开云服务器服务
new_page = cce_page.navigate_to_service("云服务器", new_tab=True)

if new_page:
    try:
        # 在新标签页中进行操作
        new_page.click("创建服务器")
        # ... 其他操作
    finally:
        # 记得关闭新标签页
        new_page.close()
```

### 2. 服务页面对象创建

```python
# 创建一个云硬盘服务的页面对象
evs_service = cce_page.create_service_page("云硬盘")

if evs_service:
    try:
        # 使用完整的页面对象功能
        evs_service.click("创建云硬盘")
        evs_service.search("test-volume")
    finally:
        evs_service.page.close()
```

### 3. 多标签页管理

```python
# 打开多个服务页面
services = ["云服务器", "云硬盘", "虚拟私有云"]
service_pages = {}

try:
    for service in services:
        page = cce_page.navigate_to_service(service, new_tab=True)
        if page:
            service_pages[service] = page
    
    # 在不同页面中进行操作
    for service, page in service_pages.items():
        # 执行特定服务的操作
        pass
        
finally:
    # 清理所有页面
    for page in service_pages.values():
        page.close()
```

### 4. 自动页面管理

```python
# 系统会自动跟踪创建的页面
cce_page.navigate_to_service("云服务器", new_tab=True)
cce_page.navigate_to_service("云硬盘", new_tab=True)

# 检查管理的页面数量
count = cce_page.get_managed_pages_count()
print(f"当前管理 {count} 个页面")

# 一次性关闭所有管理的页面
cce_page.close_all_managed_pages()
```

## API 参考

### navigate_to_service(service, new_tab=False)

**参数:**
- `service` (str): 服务名称，如 '云容器引擎'、'云硬盘'、'云服务器' 等
- `new_tab` (bool): 是否在新标签页中打开服务，默认为 False

**返回值:**
- 当 `new_tab=False` 时：返回 `bool` 表示导航是否成功
- 当 `new_tab=True` 时：返回新创建的 `Page` 对象，失败时返回 `None`

### create_service_page(service)

**参数:**
- `service` (str): 服务名称

**返回值:**
- 返回新的 `Base` 对象，如果失败返回 `None`

### close_all_managed_pages()

关闭所有由此对象创建的新标签页。

### get_managed_pages_count()

**返回值:**
- `int`: 当前管理的活跃标签页数量

## 使用场景

### 1. 跨服务测试

```python
def test_cross_service_workflow(cce_page):
    """跨服务工作流程测试"""
    
    # 在CCE中创建集群
    cce_page.click("集群管理")
    cce_page.click("创建集群")
    # ... 创建集群操作
    
    # 在新标签页中打开云服务器服务
    ecs_page = cce_page.navigate_to_service("云服务器", new_tab=True)
    
    try:
        # 在云服务器中创建实例
        ecs_page.click("创建服务器")
        # ... 创建服务器操作
        
        # 验证两个服务的关联性
        # ...
        
    finally:
        ecs_page.close()
```

### 2. 并行操作测试

```python
def test_parallel_operations(cce_page):
    """并行操作测试"""
    
    services = ["云服务器", "云硬盘", "虚拟私有云"]
    pages = {}
    
    try:
        # 同时打开多个服务
        for service in services:
            page = cce_page.navigate_to_service(service, new_tab=True)
            if page:
                pages[service] = page
        
        # 在不同服务中并行执行操作
        # 这在测试服务间的独立性时很有用
        
    finally:
        # 清理
        for page in pages.values():
            page.close()
```

### 3. 状态隔离测试

```python
def test_state_isolation(cce_page):
    """状态隔离测试"""
    
    # 在当前页面设置某种状态
    cce_page.click("集群管理")
    cce_page.search("test-cluster")
    
    # 在新标签页中验证状态隔离
    new_page = cce_page.navigate_to_service("云容器引擎", new_tab=True)
    
    try:
        # 验证新页面没有继承搜索状态
        # ...
        
    finally:
        new_page.close()
```

## 最佳实践

### 1. 资源管理

```python
# 推荐：使用 try-finally 确保页面被关闭
new_page = cce_page.navigate_to_service("云服务器", new_tab=True)
if new_page:
    try:
        # 执行操作
        pass
    finally:
        new_page.close()

# 或者使用自动管理功能
cce_page.navigate_to_service("云服务器", new_tab=True)
# ... 执行测试
cce_page.close_all_managed_pages()  # 测试结束时清理
```

### 2. 错误处理

```python
# 检查页面创建是否成功
new_page = cce_page.navigate_to_service("云服务器", new_tab=True)
if new_page is None:
    pytest.skip("无法创建新标签页，跳过测试")

# 或者使用断言
assert new_page is not None, "创建新标签页失败"
```

### 3. 向后兼容

```python
# 原有的调用方式仍然有效
result = cce_page.navigate_to_service("云服务器")  # 默认 new_tab=False
assert result is True

# 新的调用方式
new_page = cce_page.navigate_to_service("云服务器", new_tab=True)
```

## 注意事项

1. **资源管理**: 新创建的标签页需要手动关闭，避免资源泄漏
2. **浏览器限制**: 某些浏览器可能对同时打开的标签页数量有限制
3. **性能考虑**: 多标签页会增加内存使用，在大型测试套件中需要注意
4. **同步问题**: 不同标签页间的操作是独立的，需要注意时序问题

## 故障排除

### 问题：新标签页创建失败

**可能原因:**
- 服务名称不正确
- 浏览器资源不足
- 网络连接问题

**解决方案:**
- 检查服务名称是否在 `NavigationConfig.SERVICE_NAVIGATION_MAP` 中
- 确保浏览器有足够资源
- 检查网络连接

### 问题：页面操作失败

**可能原因:**
- 页面未完全加载
- 元素定位器不正确

**解决方案:**
- 添加适当的等待
- 使用正确的元素定位方法
